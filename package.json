{"name": "@logispot/backend", "version": "0.1.0", "main": "./src/index.ts", "type": "module", "private": true, "scripts": {"dev": "tsx watch --clear-screen=false --include \"./src/**/*.ts\" ./src/index.ts -- --dev", "build": "tsup", "start": "node out/index.mjs"}, "bin": {"logispot-backend": "out/index.mjs"}, "prisma": {"seed": "tsx ./prisma/seed/seed.ts"}, "devDependencies": {"@stylistic/eslint-plugin-js": "^3.1.0", "@tsconfig/strictest": "^2.0.5", "@types/bcrypt": "^5.0.2", "@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.17", "@types/express": "^5.0.1", "@types/express-requests-logger": "^4.0.0", "@types/express-session": "^1.18.1", "@types/multer": "^2.0.0", "@types/node": "^22.15.18", "@types/nodemailer": "^6.4.17", "@types/session-file-store": "^1.2.5", "@types/strong-error-handler": "^2.3.1", "@typescript-eslint/eslint-plugin": "^8.24.0", "@typescript-eslint/parser": "^8.24.0", "eslint": "^8.57.1", "prisma": "^6.9.0", "tsup": "^8.4.0", "typescript": "^5.8.3"}, "dependencies": {"@prisma/client": "^6.9.0", "axios": "^1.9.0", "bcrypt": "^5.1.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "express-requests-logger": "^4.0.3", "express-session": "^1.18.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "multer": "^2.0.2", "nodemailer": "^7.0.3", "openai": "^4.97.0", "pino": "^9.6.0", "pino-pretty": "^13.0.0", "session-file-store": "^1.5.0", "strong-error-handler": "^5.0.18", "uuid": "^11.1.0", "zod": "^3.24.4"}}