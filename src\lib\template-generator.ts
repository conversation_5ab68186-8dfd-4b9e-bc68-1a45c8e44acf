// Template generation functions with real implementation
// Based on the backend template system from backend/src/test/templates.ts

export interface TemplateContext {
  ordinal: number | null;
  service: string | null;
  incoterms: string | null;
  from: {
    country: string | null;
    city: string | null;
    address: string | null;
    zipcode: string | null;
  };
  to: {
    country: string | null;
    city: string | null;
    address: string | null;
    zipcode: string | null;
  };
  goods: {
    description: string | null;
    hsCodes: string | null;
    cost: number;
    currency: string | null;
  };
  services: {
    additional: string[];
    dangerous: string[];
  };
  packages: {
    quantity: number;
    length: number | null;
    width: number | null;
    height: number | null;
    volume: number | null;
    weight: number | null;
    type: string | null;
    isStackable: boolean;
  }[];
  total: {
    quantity: number;
    weight: number;
    volume: number;
    chargeableWeight: number;
  };
}

interface PackageData {
  quantity: number;
  length: number;
  width: number;
  height: number;
  volume: number;
  weight: number;
  type: string;
  stackable: boolean;
}

interface TemplateData {
  packages?: PackageData[];
  [key: string]: string | number | PackageData[] | undefined;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function getValue(data: any, name: string, fallbackValue = `{${name}}`) {
  console.dir({
    data,
    name,
    fallbackValue,
  }, { depth: null });

  const ternaryConditionMatch = name.match(/^(?<name>.+?) \? "(?<trueValue>.*?)" : "(?<falseValue>.*?)"$/);

  console.dir({ ternaryConditionMatch }, { depth: null });

  if (ternaryConditionMatch) {
    const { name, trueValue, falseValue } = ternaryConditionMatch.groups!;

    if (name in data) {
      const value = data[name];

      return value ? trueValue : falseValue;
    }

    return fallbackValue;
  }

  const nullishCoalescingMatch = name.match(/^(.+?)( ?? (.+?)){1,}$/);

  console.dir({ nullishCoalescingMatch }, { depth: null });

  if (nullishCoalescingMatch) {
    const names = nullishCoalescingMatch.map(name => name.trim());

    for (const name of names) {
      if (name.startsWith("\"") && name.endsWith("\"")) {
        return name.slice(1, -1);
      }

      if (name in data) {
        const value = data[name];

        return value !== null && value !== undefined ? String(value) : "";
      }
    }

    return "";
  }

  if (name in data) {
    const value = data[name];

    return value !== null && value !== undefined ? String(value) : "";
  }

  return `{${name}}`;
}

function fillTemplate(template: string, data: TemplateData): string {
  const packagesTagIndex = template.indexOf("{packages}");
  const isHasPackagesTag = packagesTagIndex !== -1;

  if (isHasPackagesTag && template.indexOf("{packages}", packagesTagIndex + 1) !== -1) {
    throw new Error("Template cannot have multiple {packages} tags");
  }

  return template
    .replace(
      /\{(.+?)\}/g,
      (_, name: string) => {
        if (name === "packages" || name === "/packages" || name.startsWith("package.")) {
          return `{${name}}`;
        }

        return getValue(data, name);
      },
    )
    .replace(
      /\{packages\}((?:.|\r?\n)+?)\{\/packages\}/gm,
      (_, match: string) => {
        return data.packages?.map(
          (_package: PackageData) => {
            return match.replace(
              /\{package\.(.+?)\}/g,
              (_, name) => {
                return getValue(_package, name, `{package.${name}}`);
              },
            );
          },
        ).join("\n") || "";
      },
    );
}

function round2(value: number) {
  return Math.round(value * 100) / 100;
}

export function getTemplateData(ctx: TemplateContext, withPackages = false): TemplateData {
  return {
    // Basic information
    "ordinal": ctx.ordinal ? `IN-${String(ctx.ordinal).padStart(3, "0")}` : "",
    "service": ctx.service || "",
    "incoterms": ctx.incoterms || "",

    // Origin (from) information
    "from.country": ctx.from.country || "",
    "from.city": ctx.from.city || "",
    "from.address": ctx.from.address || "",
    "from.zipcode": ctx.from.zipcode || "",

    // Destination (to) information
    "to.country": ctx.to.country || "",
    "to.city": ctx.to.city || "",
    "to.address": ctx.to.address || "",
    "to.zipcode": ctx.to.zipcode || "",

    // Goods information
    "goods.description": ctx.goods.description || "",
    "goods.hsCodes": ctx.goods.hsCodes || "",
    "goods.cost": ctx.goods.cost,
    "goods.currency": ctx.goods.currency || "",

    // Services information
    "services.additional": ctx.services.additional.join(", "),
    "services.dangerous": ctx.services.dangerous.join(", "),

    // Total information
    "total.quantity": String(ctx.total.quantity),
    "total.weight": String(round2(ctx.total.weight)),
    "total.volume": String(round2(ctx.total.volume)),
    "total.chargeableWeight": String(round2(ctx.total.chargeableWeight)),

    // Packages for loop processing
    packages: withPackages
      ? ctx.packages.map(pkg => ({
        quantity: pkg.quantity || 0,
        length: pkg.length || 0,
        width: pkg.width || 0,
        height: pkg.height || 0,
        volume: pkg.volume || 0,
        weight: pkg.weight || 0,
        type: pkg.type || "",
        stackable: pkg.isStackable,
      }))
      : undefined,
  };
}

export function generateResultTitle(template: string, ctx: TemplateContext): string {
  const templateData = getTemplateData(ctx, false);

  return fillTemplate(template, templateData).trim();
}

export function generateResultContent(template: string, ctx: TemplateContext): string {
  const templateData = getTemplateData(ctx, true);

  return fillTemplate(template, templateData).trim();
}

export type TemplateTag = {
  tag: string;
  description: string;
};

// Template tags for title and content fields
export const TITLE_TEMPLATE_TAGS = [
  // Basic information
  {
    tag: 'ordinal',
    description: 'Request ordinal number (e.g., IN-001)',
  },
  {
    tag: 'service',
    description: 'Requested service type (e.g., Air freight, Sea freight)',
  },
  {
    tag: 'incoterms',
    description: 'Incoterms (e.g., EXW, FOB, CIF)',
  },

  // Origin (from) information
  {
    tag: 'from.country',
    description: 'Origin country name',
  },
  {
    tag: 'from.city',
    description: 'Origin city name',
  },
  {
    tag: 'from.address',
    description: 'Complete pickup address including city, zipcode, and country',
  },
  {
    tag: 'from.zipcode',
    description: 'Origin zipcode',
  },

  // Destination (to) information
  {
    tag: 'to.country',
    description: 'Destination country name',
  },
  {
    tag: 'to.city',
    description: 'Destination city name',
  },
  {
    tag: 'to.address',
    description: 'Complete delivery address including city, zipcode, and country',
  },
  {
    tag: 'to.zipcode',
    description: 'Destination zipcode',
  },

  // Goods information
  {
    tag: 'goods.description',
    description: 'Description of goods being shipped',
  },
  {
    tag: 'goods.hsCodes',
    description: 'Harmonized System codes for customs',
  },
  {
    tag: 'goods.cost',
    description: 'Cost of goods (e.g., 1500)',
  },
  {
    tag: 'goods.currency',
    description: 'Currency code (e.g., USD, EUR)',
  },

  // Services information
  {
    tag: 'services.additional',
    description: 'Comma-separated list of additional services',
  },
  {
    tag: 'services.dangerous',
    description: 'Comma-separated list of dangerous goods',
  },

  // Total information
  {
    tag: 'total.quantity',
    description: 'Total quantity (e.g., 25)',
  },
  {
    tag: 'total.weight',
    description: 'Total weight (e.g., 150.50)',
  },
  {
    tag: 'total.volume',
    description: 'Total volume (e.g., 2.45)',
  },
  {
    tag: 'total.chargeableWeight',
    description: 'Total chargeable weight (e.g., 150.54)',
  },
];

export const CONTENT_TEMPLATE_TAGS = [
  ...TITLE_TEMPLATE_TAGS,

  // Package loop tags
  {
    tag: 'packages',
    description: 'Start of package loop - use with {/packages}. All package.* tags must be used inside {packages} loop',
  },
  {
    tag: '/packages',
    description: 'End of package loop',
  },
  {
    tag: 'package.quantity',
    description: 'Package quantity (use inside {packages} loop)',
  },
  {
    tag: 'package.length',
    description: 'Package length in cm (use inside {packages} loop)',
  },
  {
    tag: 'package.width',
    description: 'Package width in cm (use inside {packages} loop)',
  },
  {
    tag: 'package.height',
    description: 'Package height in cm (use inside {packages} loop)',
  },
  {
    tag: 'package.volume',
    description: 'Package volume in cbm (use inside {packages} loop)',
  },
  {
    tag: 'package.weight',
    description: 'Package weight in kg (use inside {packages} loop)',
  },
  {
    tag: 'package.type',
    description: 'Package type (use inside {packages} loop)',
  },
  {
    tag: 'package.stackable',
    description: 'Package stackable (use inside {packages} loop)',
  },
];
