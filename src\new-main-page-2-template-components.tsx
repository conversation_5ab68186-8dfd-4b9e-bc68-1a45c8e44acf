import type { TemplateContext, TemplateTag } from './lib/template-generator';

import { useState, useEffect, useRef } from 'react';
import {
  FormControl,
  Select,
  MenuItem,
  IconButton,
  Box,
  Typography,
  Tooltip,
  Snackbar,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Chip,
} from '@mui/material';
import { Icon } from './features/create-requests/assets';

import { Api } from './shared';
import { Requester } from "./lib/requester";
import {
  generateResultTitle,
  generateResultContent,
  TITLE_TEMPLATE_TAGS,
  CONTENT_TEMPLATE_TAGS,
} from './lib/template-generator';

type RequestTemplate = Api.GetRequestTemplatesResponse[number];

interface RequestTemplateSelectProps {
  templates: RequestTemplate[];
  selectedTemplate: RequestTemplate | null;
  onTemplateSelect: (template: RequestTemplate | null) => void;
  onTemplateEdit: (template: RequestTemplate) => void;
  onTemplateCreate: () => void;
  onTemplateDelete: (template: RequestTemplate) => void;
  loading?: boolean;
  context: TemplateContext; // Context for template generation
}

export const RequestTemplateSelect = ({
  templates,
  selectedTemplate,
  onTemplateSelect,
  onTemplateEdit,
  onTemplateCreate,
  onTemplateDelete,
  loading = false,
  context,
}: RequestTemplateSelectProps) => {
  const [copySuccess, setCopySuccess] = useState(false);

  const handleTemplateChange = (templateId: string) => {
    if (templateId === '') {
      onTemplateSelect(null);
    } else {
      const template = templates.find(t => t.id === templateId);
      if (template) {
        onTemplateSelect(template);
      }
    }
  };

  const handleCopy = async (template: RequestTemplate) => {
    try {
      const generatedTitle = generateResultTitle(template.title, context);
      const generatedContent = generateResultContent(template.content, context);
      const textToCopy = `${generatedTitle}\n\n${generatedContent}`;

      await navigator.clipboard.writeText(textToCopy);
      setCopySuccess(true);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  };

  const handleDelete = async (template: RequestTemplate) => {
    if (window.confirm(`Are you sure you want to delete template "${template.name}"?`)) {
      onTemplateDelete(template);
    }
  };

  return (
    <>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
        <FormControl size="small" sx={{ flexGrow: 1, minWidth: 200 }}>
          <Select
            value={selectedTemplate?.id || ''}
            onChange={(e) => handleTemplateChange(e.target.value)}
            displayEmpty
            disabled={loading}
            sx={{
              fontSize: '12px',
              height: '32px',
              '& .MuiOutlinedInput-notchedOutline': {
                borderRadius: 0,
              },
              '& .Mui-focused .MuiOutlinedInput-notchedOutline': {
                border: '2px solid green !important',
              },
            }}
          >
            <MenuItem value="">
              <Typography sx={{ fontSize: '12px', color: '#999' }}>
                Select template
              </Typography>
            </MenuItem>
            {templates.map((template) => (
              <MenuItem key={template.id} value={template.id}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
                  <Typography sx={{ fontSize: '12px' }}>
                    {template.name}
                  </Typography>
                  <Box
                    sx={{
                      display: 'flex',
                      gap: 0.5,
                      // Hide buttons when this template is selected (displayed in the select field)
                      '.MuiSelect-select &': {
                        display: selectedTemplate?.id === template.id ? 'none' : 'flex'
                      }
                    }}
                  >
                    <Tooltip title="Copy generated result to clipboard">
                      <IconButton
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleCopy(template);
                        }}
                        sx={{ padding: '2px' }}
                      >
                        <Icon.Copy />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Edit template">
                      <IconButton
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation();
                          onTemplateEdit(template);
                        }}
                        sx={{ padding: '2px' }}
                      >
                        <Icon.Edit />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Delete template">
                      <IconButton
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDelete(template);
                        }}
                        sx={{ padding: '2px' }}
                      >
                        <Icon.Trash />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </Box>
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        {/* Action buttons for selected template */}
        {selectedTemplate && (
          <Box sx={{ display: 'flex', gap: 0.5 }}>
            <Tooltip title="Copy generated result to clipboard">
              <IconButton
                size="small"
                onClick={() => handleCopy(selectedTemplate)}
                sx={{
                  padding: '4px',
                  width: '32px',
                  height: '32px',
                }}
              >
                <Icon.Copy />
              </IconButton>
            </Tooltip>
            <Tooltip title="Edit template">
              <IconButton
                size="small"
                onClick={() => onTemplateEdit(selectedTemplate)}
                sx={{
                  padding: '4px',
                  width: '32px',
                  height: '32px',
                }}
              >
                <Icon.Edit />
              </IconButton>
            </Tooltip>
            <Tooltip title="Delete template">
              <IconButton
                size="small"
                onClick={() => handleDelete(selectedTemplate)}
                sx={{
                  padding: '4px',
                  width: '32px',
                  height: '32px',
                }}
              >
                <Icon.Trash />
              </IconButton>
            </Tooltip>
          </Box>
        )}

        <Tooltip title="Add new template">
          <IconButton
            onClick={onTemplateCreate}
            sx={{
              backgroundColor: '#70B57D',
              color: 'white',
              width: '32px',
              height: '32px',
              '&:hover': {
                backgroundColor: '#5a9a66',
              },
            }}
          >
            <Icon.Plus />
          </IconButton>
        </Tooltip>
      </Box>

      <Snackbar
        open={copySuccess}
        autoHideDuration={2000}
        onClose={() => setCopySuccess(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={() => setCopySuccess(false)} severity="success" sx={{ width: '100%' }}>
          Text copied to clipboard!
        </Alert>
      </Snackbar>
    </>
  );
};

interface RequestTemplateModalProps {
  open: boolean;
  onClose: () => void;
  onSave: (template: RequestTemplate) => void;
  onUpdate: (template: RequestTemplate) => void;
  template?: RequestTemplate; // If provided, it"s edit mode
}

export const RequestTemplateModal = ({
  open,
  onClose,
  onSave,
  onUpdate,
  template,
}: RequestTemplateModalProps) => {
  const [name, setName] = useState("");
  const [title, setTitle] = useState("");
  const [content, setContent] = useState("");
  const [loading, setLoading] = useState(false);
  const [focusedField, setFocusedField] = useState<"title" | "content" | null>(null);

  const titleInputRef = useRef<HTMLInputElement>(null);
  const contentInputRef = useRef<HTMLTextAreaElement>(null);
  const blurTimeoutRef = useRef<number | null>(null);

  useEffect(() => {
    if (template) {
      setName(template.name);
      setTitle(template.title);
      setContent(template.content);
    } else {
      setName("");
      setTitle("");
      setContent("");
    }
  }, [template, open]);

  // Disable page scrolling when modal is open
  useEffect(() => {
    if (open) {
      // Save current scroll position
      const scrollY = window.scrollY;

      // Apply styles to disable scrolling but keep scrollbar visible
      document.body.style.position = 'fixed';
      document.body.style.top = `-${scrollY}px`;
      document.body.style.width = '100%';
      document.body.style.overflowY = 'scroll';

      return () => {
        // Restore scrolling and position
        document.body.style.position = '';
        document.body.style.top = '';
        document.body.style.width = '';
        document.body.style.overflowY = '';
        window.scrollTo(0, scrollY);
      };
    }
  }, [open]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (blurTimeoutRef.current) {
        clearTimeout(blurTimeoutRef.current);
      }
    };
  }, []);

  const handleSave = async () => {
    if (!name.trim() || !title.trim() || !content.trim()) {
      return;
    }

    setLoading(true);
    try {
      if (template) {
        // Edit mode
        const updateData: Api.UpdateRequestTemplateRequestBody = {
          name: name.trim(),
          title: title.trim(),
          content: content.trim(),
        };
        const result = await Requester.put<RequestTemplate>(`/request/template/${template.id}`, updateData);
        if (result.success) {
          onUpdate(result.body);
          onClose();
        }
      } else {
        // Create mode
        const createData: Api.CreateRequestTemplateRequest = {
          name: name.trim(),
          title: title.trim(),
          content: content.trim(),
        };
        const result = await Requester.post<RequestTemplate>("/request/template", createData);
        if (result.success) {
          onSave(result.body);
          onClose();
        }
      }
    } catch (error) {
      console.error("Failed to save template:", error);
    } finally {
      setLoading(false);
    }
  };

  const insertTag = (tag: string) => {
    // Clear any pending blur timeout
    if (blurTimeoutRef.current) {
      clearTimeout(blurTimeoutRef.current);
      blurTimeoutRef.current = null;
    }

    if (focusedField === "title" && titleInputRef.current) {
      const input = titleInputRef.current;
      const start = input.selectionStart || 0;
      const end = input.selectionEnd || 0;
      const newValue = title.substring(0, start) + tag + title.substring(end);
      setTitle(newValue);

      // Set cursor position after the inserted tag
      setTimeout(() => {
        input.focus();
        input.setSelectionRange(start + tag.length, start + tag.length);
      }, 0);
    } else if (focusedField === "content" && contentInputRef.current) {
      const input = contentInputRef.current;
      const start = input.selectionStart || 0;
      const end = input.selectionEnd || 0;
      const newValue = content.substring(0, start) + tag + content.substring(end);
      setContent(newValue);

      // Set cursor position after the inserted tag
      setTimeout(() => {
        input.focus();
        input.setSelectionRange(start + tag.length, start + tag.length);
      }, 0);
    }
  };

  const getAvailableTags = (): TemplateTag[] => {
    if (focusedField === "title") {
      return TITLE_TEMPLATE_TAGS;
    } else if (focusedField === "content") {
      return CONTENT_TEMPLATE_TAGS;
    }
    return [];
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      slotProps={{
        paper: {
          sx: {
            height: "80vh",
            maxHeight: "800px",
          },
        },
      }}
    >
      <DialogTitle>
        <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
          <Typography variant="h6">
            {template ? "Edit Template" : "Create Template"}
          </Typography>
          <IconButton onClick={onClose}>
            <Icon.Close />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent sx={{ display: "flex", gap: 2, height: "100%" }}>
        <Box sx={{ flex: 1, display: "flex", flexDirection: "column", gap: 2 }}>
          <TextField
            label="Template Name"
            value={name}
            onChange={(e) => setName(e.target.value)}
            fullWidth
            size="small"
            required
            sx={{
              "& .MuiOutlinedInput-root": {
                borderRadius: 0,
                fontSize: "12px",
              },
              "& .Mui-focused .MuiOutlinedInput-notchedOutline": {
                border: "2px solid green !important",
              },
            }}
          />

          <TextField
            label="Template Title"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            onFocus={() => {
              if (blurTimeoutRef.current) {
                clearTimeout(blurTimeoutRef.current);
                blurTimeoutRef.current = null;
              }
              setFocusedField("title");
            }}
            onBlur={() => {
              blurTimeoutRef.current = window.setTimeout(() => {
                setFocusedField(null);
              }, 150);
            }}
            inputRef={titleInputRef}
            fullWidth
            size="small"
            required
            sx={{
              "& .MuiOutlinedInput-root": {
                borderRadius: 0,
                fontSize: "12px",
              },
              "& .Mui-focused .MuiOutlinedInput-notchedOutline": {
                border: "2px solid green !important",
              },
            }}
          />

          <TextField
            label="Template Content"
            value={content}
            onChange={(e) => setContent(e.target.value)}
            onFocus={() => {
              if (blurTimeoutRef.current) {
                clearTimeout(blurTimeoutRef.current);
                blurTimeoutRef.current = null;
              }
              setFocusedField("content");
            }}
            onBlur={() => {
              blurTimeoutRef.current = window.setTimeout(() => {
                setFocusedField(null);
              }, 150);
            }}
            inputRef={contentInputRef}
            multiline
            rows={12}
            fullWidth
            required
            sx={{
              flex: 1,
              "& .MuiOutlinedInput-root": {
                borderRadius: 0,
                fontSize: "12px",
                alignItems: "flex-start",
              },
              "& .MuiInputBase-input": {
                resize: "none",
              },
              "& .Mui-focused .MuiOutlinedInput-notchedOutline": {
                border: "2px solid green !important",
              },
            }}
          />
        </Box>

        {focusedField && (
          <Box sx={{ width: "300px", borderLeft: "1px solid #e0e0e0", pl: 2 }}>
            <Typography variant="subtitle2" sx={{ mb: 2, fontWeight: 600 }}>
              Available Tags
            </Typography>
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                gap: 1,
                maxHeight: "400px",
                overflowY: "auto",
                // Custom scrollbar styling
                "&::-webkit-scrollbar": {
                  width: "8px",
                },
                "&::-webkit-scrollbar-track": {
                  backgroundColor: "#f1f1f1",
                  borderRadius: "4px",
                },
                "&::-webkit-scrollbar-thumb": {
                  backgroundColor: "#c1c1c1",
                  borderRadius: "4px",
                  "&:hover": {
                    backgroundColor: "#a8a8a8",
                  },
                },
                // Firefox scrollbar styling
                scrollbarWidth: "thin",
                scrollbarColor: "#c1c1c1 #f1f1f1",
              }}
            >
              {getAvailableTags().map((tagInfo) => (
                <Tooltip key={tagInfo.tag} title={tagInfo.description} placement="left">
                  <Chip
                    label={`{${tagInfo.tag}}`}
                    onClick={() => insertTag(`{${tagInfo.tag}}`)}
                    sx={{
                      fontSize: "11px",
                      height: "28px",
                      cursor: "pointer",
                      "&:hover": {
                        backgroundColor: "#70B57D",
                        color: "white",
                      },
                    }}
                  />
                </Tooltip>
              ))}
            </Box>
          </Box>
        )}
      </DialogContent>

      <DialogActions sx={{ p: 2 }}>
        <Button
          onClick={handleSave}
          disabled={loading || !name.trim() || !title.trim() || !content.trim()}
          variant="contained"
          sx={{
            backgroundColor: "#70B57D",
            color: "white",
            textTransform: "none",
            "&:hover": {
              backgroundColor: "#5a9a66",
            },
            "&:disabled": {
              backgroundColor: "#ccc",
            },
          }}
        >
          {loading ? "Saving..." : "Save"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};
