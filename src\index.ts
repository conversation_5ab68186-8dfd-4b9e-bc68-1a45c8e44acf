import pino from "pino";
import dotenv from "dotenv";
import * as Di from "./utils/di";
import { Config } from "./config";
import { Server } from "./server";
import { ExtractService } from "./extract";
import { OpenAiService } from "./llm/openai";
import { AuthService } from "./auth";
import { UserService } from "./user";
import { EmailService } from "./email";
import { PrismaClient } from "@prisma/client";
import { RequestService } from "./request";
import { RequestTemplateController, RequestTemplateService } from "./request-template";

dotenv.config();

const isDev = process.argv.includes("--dev");

const logger = pino({
    level: isDev ? "debug" : "info",
    transport: {
        target: "pino-pretty",
        options: {
            colorize: true,
        },
    },
});

const containerInitialData = {
    isDev: { asValue: isDev },
    logger: { asValue: logger },

    server: { asClass: Server },
    config: { asClass: Config },

    prisma: { asValue: new PrismaClient() },

    extractService: { asClass: ExtractService },
    llmService: { asClass: OpenAiService },
    emailService: { asClass: EmailService },
    userService: { asClass: UserService },
    authService: { asClass: AuthService },
    requestService: { asClass: RequestService },
    requestTemplateService: { asClass: RequestTemplateService },

    requestTemplateController: { asClass: RequestTemplateController },
} satisfies Di.ContainerInitialData;

export type ContainerData = Di.ContainerData<typeof containerInitialData>;

const container = await new Di.Container<ContainerData>()
    .register(containerInitialData)
    .inject()
    .init();

const server = container.resolve("server");

server.run();
