import type { LlmService } from "../llm";

import fs from "fs/promises";
import { Injectable } from "../utils";
import * as DtoV1 from "./dto-v1";
import * as DtoV2 from "./dto-v2";
import * as DtoV3 from "./dto-v3";
import * as Prompt from "./prompts";

export class ExtractService extends Injectable {
    private readonly llmService!: LlmService;

    async extractV1(data: {
        text: string;
    }) {
        return await this.llmService.chatCompletion({
            messages: [
                {
                    role: "system",
                    content: Prompt.v1,
                },
                {
                    role: "user",
                    content: data.text,
                },
            ],
            responseFormat: {
                schema: DtoV1.Extract,
                name: "extract",
            },
        });
    }

    async extractV2(data: {
        text: string;
    }) {
        const [
            metadata,
            digits,
            services,
        ] = await Promise.all([
            this.llmService.chatCompletion({
                messages: [
                    {
                        role: "system",
                        content: Prompt.v2.metadata,
                    },
                    {
                        role: "user",
                        content: data.text,
                    },
                ],
                responseFormat: {
                    schema: DtoV2.ExtractMetadata,
                    name: "metadata",
                },
            }),

            this.llmService.chatCompletion({
                messages: [
                    {
                        role: "system",
                        content: Prompt.v2.digits,
                    },
                    {
                        role: "user",
                        content: data.text,
                    },
                ],
                responseFormat: {
                    schema: DtoV2.ExtractDigits,
                    name: "digits",
                },
            }),

            this.llmService.chatCompletion({
                messages: [
                    {
                        role: "system",
                        content: Prompt.v2.services,
                    },
                    {
                        role: "user",
                        content: data.text,
                    },
                ],
                responseFormat: {
                    schema: DtoV2.ExtractServices,
                    name: "services",
                },
            }),
        ]);

        return {
            metadata,
            digits,
            services,
        };
    }

    async extractV3(data: {
        text: string;
        files: {
            name: string;
            content: Buffer;
        }[];
    }) {
        const files = await Promise.all(
            data.files.map(async (file) => ({
                name: file.name,
                content: file.content.toString("base64"),
            })),
        );

        const llmFiles = files.map((file) => ({
            type: "file" as const,
            file: {
                filename: file.name,
                file_data: file.content,
            },
        }));

        const userMessage = {
            role: "user" as const,
            content: [
                {
                    type: "text" as const,
                    text: data.text,
                },
                ...llmFiles,
            ],
        };

        const [
            metadata,
            digits,
            services,
        ] = await Promise.all([
            this.llmService.chatCompletion({
                messages: [
                    {
                        role: "system",
                        content: Prompt.v3.metadata,
                    },
                    userMessage,
                ],
                responseFormat: {
                    schema: DtoV2.ExtractMetadata,
                    name: "metadata",
                },
            }),

            this.llmService.chatCompletion({
                messages: [
                    {
                        role: "system",
                        content: Prompt.v3.digits,
                    },
                    userMessage,
                ],
                responseFormat: {
                    schema: DtoV2.ExtractDigits,
                    name: "digits",
                },
            }),

            this.llmService.chatCompletion({
                messages: [
                    {
                        role: "system",
                        content: Prompt.v3.services,
                    },
                    userMessage,
                ],
                responseFormat: {
                    schema: DtoV2.ExtractServices,
                    name: "services",
                },
            }),
        ]);

        return {
            metadata,
            digits,
            services,
        };
    }
}
